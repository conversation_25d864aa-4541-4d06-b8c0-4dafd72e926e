import { Module } from '@nestjs/common';
import { AssetAdjustmentsService } from './asset-adjustments.service';
import { AssetAdjustmentsController } from './asset-adjustments.controller';
import { AuthModule } from '../auth/auth.module';
import { DrizzleModule } from '../drizzle/drizzle.module';
import { ActivityLogModule } from '../activity-log/activity-log.module';
import { SharedServicesModule } from '../shared/services/shared-services.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    AuthModule,
    DrizzleModule,
    ActivityLogModule,
    SharedServicesModule,
    UsersModule,
  ],
  controllers: [AssetAdjustmentsController],
  providers: [AssetAdjustmentsService],
  exports: [AssetAdjustmentsService],
})
export class AssetAdjustmentsModule {}
