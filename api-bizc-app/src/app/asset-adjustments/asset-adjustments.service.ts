import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAssetAdjustmentDto } from './dto/create-asset-adjustment.dto';
import { UpdateAssetAdjustmentDto } from './dto/update-asset-adjustment.dto';
import { AssetAdjustmentDto } from './dto/asset-adjustment.dto';
import { AssetAdjustmentSlimDto } from './dto/asset-adjustment-slim.dto';
import { AssetAdjustmentListDto } from './dto/asset-adjustment-list.dto';
import { assetAdjustments } from '../drizzle/schema/asset-adjustments.schema';
import { assets } from '../drizzle/schema/assets.schema';
import { journalEntries } from '../drizzle/schema/journal-entries.schema';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { AuditFieldsService } from '../shared/services/audit-fields.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AssetAdjustmentsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly auditFieldsService: AuditFieldsService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAssetAdjustmentDto: CreateAssetAdjustmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify the asset exists and belongs to the business
      const asset = await this.db
        .select()
        .from(assets)
        .where(
          and(
            eq(assets.id, createAssetAdjustmentDto.assetId),
            eq(assets.businessId, businessId),
            eq(assets.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!asset) {
        throw new BadRequestException(
          'Asset not found or does not belong to this business',
        );
      }

      // Validate journal entry if provided
      if (createAssetAdjustmentDto.journalEntryId) {
        const journalEntry = await this.db
          .select()
          .from(journalEntries)
          .where(eq(journalEntries.id, createAssetAdjustmentDto.journalEntryId))
          .then((results) => results[0]);

        if (!journalEntry) {
          throw new BadRequestException('Journal entry not found');
        }
      }

      // Create the asset adjustment
      const [newAssetAdjustment] = await this.db
        .insert(assetAdjustments)
        .values({
          businessId,
          assetId: createAssetAdjustmentDto.assetId,
          adjustmentType: createAssetAdjustmentDto.adjustmentType,
          adjustmentDate: createAssetAdjustmentDto.adjustmentDate,
          effectivePeriod: createAssetAdjustmentDto.effectivePeriod,
          amount: createAssetAdjustmentDto.amount,
          bookValueBefore: createAssetAdjustmentDto.bookValueBefore,
          bookValueAfter: createAssetAdjustmentDto.bookValueAfter,
          description: createAssetAdjustmentDto.description,
          referenceNo: createAssetAdjustmentDto.referenceNo,
          status: createAssetAdjustmentDto.status ?? 'posted',
          journalEntryId: createAssetAdjustmentDto.journalEntryId,
          createdBy: userId,
        })
        .returning();

      // Log the asset adjustment creation activity (fire-and-forget)
      void this.activityLogService
        .logCreate(
          newAssetAdjustment.id,
          EntityType.ASSET,
          userId,
          businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        )
        .catch((error) => {
          console.error(
            `Failed to log activity for asset adjustment ${newAssetAdjustment.id}:`,
            error,
          );
        });

      return {
        id: newAssetAdjustment.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create asset adjustment: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: AssetAdjustmentDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(assetAdjustments.isDeleted, false),
      eq(assetAdjustments.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetAdjustments.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assetAdjustments.createdAt, toDate));
      }
    }

    // Find all asset adjustments for the user's active business with pagination
    const result = await this.db
      .select({
        id: assetAdjustments.id,
        businessId: assetAdjustments.businessId,
        assetId: assetAdjustments.assetId,
        adjustmentType: assetAdjustments.adjustmentType,
        adjustmentDate: assetAdjustments.adjustmentDate,
        effectivePeriod: assetAdjustments.effectivePeriod,
        amount: assetAdjustments.amount,
        bookValueBefore: assetAdjustments.bookValueBefore,
        bookValueAfter: assetAdjustments.bookValueAfter,
        description: assetAdjustments.description,
        referenceNo: assetAdjustments.referenceNo,
        status: assetAdjustments.status,
        journalEntryId: assetAdjustments.journalEntryId,
        createdBy: assetAdjustments.createdBy,
        updatedBy: assetAdjustments.updatedBy,
        createdAt: assetAdjustments.createdAt,
        updatedAt: assetAdjustments.updatedAt,
        isDeleted: assetAdjustments.isDeleted,
      })
      .from(assetAdjustments)
      .where(and(...whereConditions))
      .orderBy(desc(assetAdjustments.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assetAdjustments)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: await Promise.all(
        result.map((assetAdjustment: any) =>
          this.mapToAssetAdjustmentDto(assetAdjustment),
        ),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    assetId?: string,
    adjustmentType?: string,
    status?: string,
    referenceNo?: string,
    sort?: string,
  ): Promise<{
    data: AssetAdjustmentListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(assetAdjustments.isDeleted, false),
      eq(assetAdjustments.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetAdjustments.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assetAdjustments.createdAt, toDate));
      }
    }

    // Add asset filtering if provided
    if (assetId) {
      whereConditions.push(eq(assetAdjustments.assetId, assetId));
    }

    // Add adjustment type filtering if provided
    if (adjustmentType) {
      const decodedAdjustmentType = decodeURIComponent(adjustmentType);
      const adjustmentTypeArray = decodedAdjustmentType
        .split(',')
        .map((s) => s.trim());
      whereConditions.push(
        inArray(assetAdjustments.adjustmentType, adjustmentTypeArray),
      );
    }

    // Add status filtering if provided
    if (status) {
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus.split(',').map((s) => s.trim());
      whereConditions.push(inArray(assetAdjustments.status, statusArray));
    }

    // Add reference number filtering if provided
    if (referenceNo) {
      whereConditions.push(
        ilike(assetAdjustments.referenceNo, `%${referenceNo}%`),
      );
    }

    // Determine sort order
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'adjustmentDate':
          orderBy = isDesc
            ? desc(assetAdjustments.adjustmentDate)
            : asc(assetAdjustments.adjustmentDate);
          break;
        case 'amount':
          orderBy = isDesc
            ? desc(assetAdjustments.amount)
            : asc(assetAdjustments.amount);
          break;
        case 'adjustmentType':
          orderBy = isDesc
            ? desc(assetAdjustments.adjustmentType)
            : asc(assetAdjustments.adjustmentType);
          break;
        case 'status':
          orderBy = isDesc
            ? desc(assetAdjustments.status)
            : asc(assetAdjustments.status);
          break;
        default:
          orderBy = desc(assetAdjustments.createdAt);
      }
    } else {
      orderBy = desc(assetAdjustments.createdAt);
    }

    // Query with joins to get asset information
    const result = await this.db
      .select({
        id: assetAdjustments.id,
        assetId: assetAdjustments.assetId,
        assetName: assets.name,
        assetCode: assets.assetCode,
        adjustmentType: assetAdjustments.adjustmentType,
        adjustmentDate: assetAdjustments.adjustmentDate,
        effectivePeriod: assetAdjustments.effectivePeriod,
        amount: assetAdjustments.amount,
        bookValueBefore: assetAdjustments.bookValueBefore,
        bookValueAfter: assetAdjustments.bookValueAfter,
        status: assetAdjustments.status,
        referenceNo: assetAdjustments.referenceNo,
        createdAt: assetAdjustments.createdAt,
        updatedAt: assetAdjustments.updatedAt,
      })
      .from(assetAdjustments)
      .leftJoin(assets, eq(assetAdjustments.assetId, assets.id))
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assetAdjustments)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: result.map((row: any) => ({
        id: row.id,
        asset: {
          id: row.assetId,
          name: row.assetName,
          assetCode: row.assetCode,
        },
        adjustmentType: row.adjustmentType,
        adjustmentDate: row.adjustmentDate,
        effectivePeriod: row.effectivePeriod,
        amount: row.amount,
        bookValueBefore: row.bookValueBefore,
        bookValueAfter: row.bookValueAfter,
        status: row.status,
        referenceNo: row.referenceNo,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
      })),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(userId: string, id: string): Promise<AssetAdjustmentDto> {
    // Get the asset adjustment
    const assetAdjustment = await this.db
      .select()
      .from(assetAdjustments)
      .where(
        and(eq(assetAdjustments.id, id), eq(assetAdjustments.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!assetAdjustment) {
      throw new NotFoundException(`Asset adjustment with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== assetAdjustment.businessId
    ) {
      throw new UnauthorizedException('Access denied to this asset adjustment');
    }

    return await this.mapToAssetAdjustmentDto(assetAdjustment);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetAdjustmentDto: UpdateAssetAdjustmentDto,
    metadata?: ActivityMetadata,
  ): Promise<AssetAdjustmentDto> {
    try {
      // Get the asset adjustment
      const existingAssetAdjustment = await this.db
        .select()
        .from(assetAdjustments)
        .where(
          and(
            eq(assetAdjustments.id, id),
            eq(assetAdjustments.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingAssetAdjustment) {
        throw new NotFoundException(`Asset adjustment with ID ${id} not found`);
      }

      // Verify business ownership
      if (businessId !== existingAssetAdjustment.businessId) {
        throw new UnauthorizedException(
          'Access denied to update this asset adjustment',
        );
      }

      // Verify the asset exists and belongs to the business if assetId is being updated
      if (
        updateAssetAdjustmentDto.assetId &&
        updateAssetAdjustmentDto.assetId !== existingAssetAdjustment.assetId
      ) {
        const asset = await this.db
          .select()
          .from(assets)
          .where(
            and(
              eq(assets.id, updateAssetAdjustmentDto.assetId),
              eq(assets.businessId, businessId),
              eq(assets.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!asset) {
          throw new BadRequestException(
            'Asset not found or does not belong to this business',
          );
        }
      }

      // Validate journal entry if provided
      if (updateAssetAdjustmentDto.journalEntryId) {
        const journalEntry = await this.db
          .select()
          .from(journalEntries)
          .where(eq(journalEntries.id, updateAssetAdjustmentDto.journalEntryId))
          .then((results) => results[0]);

        if (!journalEntry) {
          throw new BadRequestException('Journal entry not found');
        }
      }

      // Update the asset adjustment
      const [updatedAssetAdjustment] = await this.db
        .update(assetAdjustments)
        .set({
          ...updateAssetAdjustmentDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(assetAdjustments.id, id))
        .returning();

      // Log the activity (fire-and-forget)
      void this.activityLogService
        .logUpdate(id, EntityType.ASSET, userId, businessId, {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        })
        .catch((error) => {
          console.error(
            `Failed to log activity for asset adjustment update ${id}:`,
            error,
          );
        });

      return await this.mapToAssetAdjustmentDto(updatedAssetAdjustment);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update asset adjustment: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the asset adjustment
    const existingAssetAdjustment = await this.db
      .select()
      .from(assetAdjustments)
      .where(
        and(eq(assetAdjustments.id, id), eq(assetAdjustments.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!existingAssetAdjustment) {
      throw new NotFoundException(`Asset adjustment with ID ${id} not found`);
    }

    if (businessId !== existingAssetAdjustment.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this asset adjustment',
      );
    }

    // Soft delete the asset adjustment
    await this.db
      .update(assetAdjustments)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(assetAdjustments.id, id));

    // Log the activity (fire-and-forget)
    void this.activityLogService
      .logDelete(id, EntityType.ASSET, userId, businessId, {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      })
      .catch((error) => {
        console.error(
          `Failed to log activity for asset adjustment deletion ${id}:`,
          error,
        );
      });

    return {
      success: true,
      message: `Asset adjustment with ID ${id} has been deleted`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    assetAdjustmentIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!assetAdjustmentIds || assetAdjustmentIds.length === 0) {
        throw new BadRequestException(
          'No asset adjustment IDs provided for deletion',
        );
      }

      // Get all asset adjustments that exist and belong to the business
      const existingAssetAdjustments = await this.db
        .select({
          id: assetAdjustments.id,
          businessId: assetAdjustments.businessId,
        })
        .from(assetAdjustments)
        .where(
          and(
            inArray(assetAdjustments.id, assetAdjustmentIds),
            eq(assetAdjustments.businessId, businessId),
            eq(assetAdjustments.isDeleted, false),
          ),
        );

      if (existingAssetAdjustments.length === 0) {
        throw new NotFoundException(
          'No valid asset adjustments found for deletion',
        );
      }

      const validIds = existingAssetAdjustments.map((adj) => adj.id);

      // Perform bulk soft delete
      await this.db
        .update(assetAdjustments)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(assetAdjustments.id, validIds));

      // Log bulk delete activity (fire-and-forget)
      void this.activityLogService
        .logBulkDelete(validIds, EntityType.ASSET, userId, businessId, {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        })
        .catch((error) => {
          console.error(
            `Failed to log bulk delete activity for asset adjustments:`,
            error,
          );
        });

      return {
        deleted: validIds.length,
        message: `Successfully deleted ${validIds.length} asset adjustment(s)`,
        deletedIds: validIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete asset adjustments: ${error.message}`,
      );
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createAssetAdjustmentDtos: CreateAssetAdjustmentDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (
        !createAssetAdjustmentDtos ||
        createAssetAdjustmentDtos.length === 0
      ) {
        throw new BadRequestException(
          'No asset adjustments provided for creation',
        );
      }

      const createdIds: string[] = [];

      // Process each asset adjustment in a transaction
      await this.db.transaction(async (tx) => {
        for (const createAssetAdjustmentDto of createAssetAdjustmentDtos) {
          // Verify the asset exists and belongs to the business
          const asset = await tx
            .select()
            .from(assets)
            .where(
              and(
                eq(assets.id, createAssetAdjustmentDto.assetId),
                eq(assets.businessId, businessId),
                eq(assets.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (!asset) {
            throw new BadRequestException(
              `Asset with ID ${createAssetAdjustmentDto.assetId} not found or does not belong to this business`,
            );
          }

          // Validate journal entry if provided
          if (createAssetAdjustmentDto.journalEntryId) {
            const journalEntry = await tx
              .select()
              .from(journalEntries)
              .where(
                eq(journalEntries.id, createAssetAdjustmentDto.journalEntryId),
              )
              .then((results) => results[0]);

            if (!journalEntry) {
              throw new BadRequestException(
                `Journal entry with ID ${createAssetAdjustmentDto.journalEntryId} not found`,
              );
            }
          }

          // Create the asset adjustment
          const [newAssetAdjustment] = await tx
            .insert(assetAdjustments)
            .values({
              businessId,
              assetId: createAssetAdjustmentDto.assetId,
              adjustmentType: createAssetAdjustmentDto.adjustmentType,
              adjustmentDate: createAssetAdjustmentDto.adjustmentDate,
              effectivePeriod: createAssetAdjustmentDto.effectivePeriod,
              amount: createAssetAdjustmentDto.amount,
              bookValueBefore: createAssetAdjustmentDto.bookValueBefore,
              bookValueAfter: createAssetAdjustmentDto.bookValueAfter,
              description: createAssetAdjustmentDto.description,
              referenceNo: createAssetAdjustmentDto.referenceNo,
              status: createAssetAdjustmentDto.status ?? 'posted',
              journalEntryId: createAssetAdjustmentDto.journalEntryId,
              createdBy: userId,
            })
            .returning();

          createdIds.push(newAssetAdjustment.id);
        }
      });

      // Log bulk create operation (fire-and-forget)
      void this.activityLogService
        .logBulkCreate(createdIds, EntityType.ASSET, userId, businessId, {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        })
        .catch((error) => {
          console.error(
            `Failed to log bulk create activity for asset adjustments:`,
            error,
          );
        });

      return {
        ids: createdIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create asset adjustments: ${error.message}`,
      );
    }
  }

  // Helper methods for creating and returning IDs
  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAssetAdjustmentDto: CreateAssetAdjustmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createAssetAdjustmentDto, metadata);
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createAssetAdjustmentDtos: CreateAssetAdjustmentDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    return this.bulkCreate(
      userId,
      businessId,
      createAssetAdjustmentDtos,
      metadata,
    );
  }

  private async mapToAssetAdjustmentDto(
    assetAdjustment: typeof assetAdjustments.$inferSelect,
  ): Promise<AssetAdjustmentDto> {
    // Get asset information
    const asset = await this.db
      .select({
        id: assets.id,
        name: assets.name,
        assetCode: assets.assetCode,
      })
      .from(assets)
      .where(eq(assets.id, assetAdjustment.assetId))
      .then((results) => results[0]);

    // Get journal entry information if linked
    let journalEntry:
      | { id: string; entryNumber: string; description?: string }
      | undefined;
    if (assetAdjustment.journalEntryId) {
      const journalEntryData = await this.db
        .select({
          id: journalEntries.id,
          entryNumber: journalEntries.entryNumber,
          description: journalEntries.description,
        })
        .from(journalEntries)
        .where(eq(journalEntries.id, assetAdjustment.journalEntryId))
        .then((results) => results[0]);

      if (journalEntryData) {
        journalEntry = {
          id: journalEntryData.id,
          entryNumber: journalEntryData.entryNumber,
          description: journalEntryData.description,
        };
      }
    }

    const baseAssetAdjustmentDto = {
      id: assetAdjustment.id.toString(),
      businessId: assetAdjustment.businessId.toString(),
      assetId: assetAdjustment.assetId.toString(),
      asset: {
        id: asset?.id || assetAdjustment.assetId,
        name: asset?.name || 'Unknown Asset',
        assetCode: asset?.assetCode,
      },
      adjustmentType: assetAdjustment.adjustmentType,
      adjustmentDate: assetAdjustment.adjustmentDate,
      effectivePeriod: assetAdjustment.effectivePeriod,
      amount: assetAdjustment.amount,
      bookValueBefore: assetAdjustment.bookValueBefore,
      bookValueAfter: assetAdjustment.bookValueAfter,
      description: assetAdjustment.description,
      referenceNo: assetAdjustment.referenceNo,
      status: assetAdjustment.status,
      journalEntryId: assetAdjustment.journalEntryId,
      journalEntry,
    };

    const assetAdjustmentDto =
      await this.auditFieldsService.populateAuditFields(
        assetAdjustment,
        baseAssetAdjustmentDto,
      );

    return assetAdjustmentDto;
  }
}
