import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AssetAdjustmentsService } from './asset-adjustments.service';
import { CreateAssetAdjustmentDto } from './dto/create-asset-adjustment.dto';
import { UpdateAssetAdjustmentDto } from './dto/update-asset-adjustment.dto';
import { AssetAdjustmentDto } from './dto/asset-adjustment.dto';
import { AssetAdjustmentSlimDto } from './dto/asset-adjustment-slim.dto';
import { AssetAdjustmentIdResponseDto } from './dto/asset-adjustment-id-response.dto';
import { BulkAssetAdjustmentIdsResponseDto } from './dto/bulk-asset-adjustment-ids-response.dto';
import { BulkCreateAssetAdjustmentDto } from './dto/bulk-create-asset-adjustment.dto';
import { DeleteAssetAdjustmentResponseDto } from './dto/delete-asset-adjustment-response.dto';
import { BulkDeleteAssetAdjustmentDto } from './dto/bulk-delete-asset-adjustment.dto';
import { BulkDeleteAssetAdjustmentResponseDto } from './dto/bulk-delete-asset-adjustment-response.dto';
import { PaginatedAssetAdjustmentsResponseDto } from './dto/paginated-asset-adjustments-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';
import type { AuthenticatedRequest } from '../shared/types';

@ApiTags('asset-adjustments')
@Controller('asset-adjustments')
@UseGuards(PermissionsGuard)
export class AssetAdjustmentsController {
  constructor(
    private readonly assetAdjustmentsService: AssetAdjustmentsService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_ADJUSTMENT_CREATE)
  @ApiOperation({ summary: 'Create a new asset adjustment' })
  @ApiBody({
    description: 'Asset adjustment creation data',
    type: CreateAssetAdjustmentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The asset adjustment has been successfully created',
    type: AssetAdjustmentIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  create(
    @Request() req: AuthenticatedRequest,
    @Body() createAssetAdjustmentDto: CreateAssetAdjustmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AssetAdjustmentIdResponseDto> {
    return this.assetAdjustmentsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAssetAdjustmentDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_ADJUSTMENT_CREATE)
  @ApiOperation({ summary: 'Bulk create asset adjustments' })
  @ApiBody({
    description: 'Bulk asset adjustment creation data',
    type: BulkCreateAssetAdjustmentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The asset adjustments have been successfully created',
    type: BulkAssetAdjustmentIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or assets not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkCreate(
    @Request() req: AuthenticatedRequest,
    @Body() bulkCreateAssetAdjustmentDto: BulkCreateAssetAdjustmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkAssetAdjustmentIdsResponseDto> {
    return this.assetAdjustmentsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAssetAdjustmentDto.assetAdjustments,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_ADJUSTMENT_READ)
  @ApiOperation({
    summary: 'Get all asset adjustments for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (ISO 8601 format)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (ISO 8601 format)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'assetId',
    description: 'Filter by asset ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'adjustmentType',
    description: 'Filter by adjustment type (comma-separated for multiple)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status (comma-separated for multiple)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'referenceNo',
    description: 'Filter by reference number (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort by field:direction (e.g., adjustmentDate:desc, amount:asc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'List of asset adjustments retrieved successfully',
    type: PaginatedAssetAdjustmentsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: AuthenticatedRequest,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('assetId') assetId?: string,
    @Query('adjustmentType') adjustmentType?: string,
    @Query('status') status?: string,
    @Query('referenceNo') referenceNo?: string,
    @Query('sort') sort?: string,
  ): Promise<PaginatedAssetAdjustmentsResponseDto> {
    return this.assetAdjustmentsService.findAllOptimized(
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      assetId,
      adjustmentType,
      status,
      referenceNo,
      sort,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_ADJUSTMENT_READ)
  @ApiOperation({ summary: 'Get a specific asset adjustment by ID' })
  @ApiParam({
    name: 'id',
    description: 'Asset adjustment ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset adjustment retrieved successfully',
    type: AssetAdjustmentDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset adjustment not found',
  })
  findOne(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
  ): Promise<AssetAdjustmentDto> {
    return this.assetAdjustmentsService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_ADJUSTMENT_UPDATE)
  @ApiOperation({ summary: 'Update an asset adjustment' })
  @ApiParam({
    name: 'id',
    description: 'Asset adjustment ID',
    type: String,
  })
  @ApiBody({
    description: 'Asset adjustment update data',
    type: UpdateAssetAdjustmentDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset adjustment updated successfully',
    type: AssetAdjustmentDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset adjustment not found',
  })
  update(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() updateAssetAdjustmentDto: UpdateAssetAdjustmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AssetAdjustmentDto> {
    return this.assetAdjustmentsService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAssetAdjustmentDto,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_ADJUSTMENT_DELETE)
  @ApiOperation({ summary: 'Delete an asset adjustment' })
  @ApiParam({
    name: 'id',
    description: 'Asset adjustment ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset adjustment deleted successfully',
    type: DeleteAssetAdjustmentResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset adjustment not found',
  })
  async remove(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAssetAdjustmentResponseDto> {
    const result = await this.assetAdjustmentsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
    return {
      message: result.message,
      id: id,
    };
  }

  @Delete('bulk/delete')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_ADJUSTMENT_DELETE)
  @ApiOperation({ summary: 'Bulk delete asset adjustments' })
  @ApiBody({
    description: 'Asset adjustment IDs to delete',
    type: BulkDeleteAssetAdjustmentDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset adjustments deleted successfully',
    type: BulkDeleteAssetAdjustmentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No IDs provided',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No valid asset adjustments found for deletion',
  })
  async bulkDelete(
    @Request() req: AuthenticatedRequest,
    @Body() bulkDeleteAssetAdjustmentDto: BulkDeleteAssetAdjustmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAssetAdjustmentResponseDto> {
    const result = await this.assetAdjustmentsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAssetAdjustmentDto.ids,
      metadata,
    );
    return {
      deletedCount: result.deleted,
      deletedIds: result.deletedIds,
    };
  }
}
