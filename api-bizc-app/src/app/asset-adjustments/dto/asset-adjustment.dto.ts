import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AssetAdjustmentType } from '../../drizzle/schema/asset-adjustments.schema';
import { AuditFieldsDto } from '../../shared/dto/audit-fields.dto';

export class AssetAdjustmentDto extends AuditFieldsDto {
  @ApiProperty({ description: 'Unique identifier for the asset adjustment' })
  id: string;

  @ApiProperty({ description: 'Business ID the asset adjustment belongs to' })
  businessId: string;

  @ApiProperty({ description: 'Asset ID being adjusted' })
  assetId: string;

  @ApiProperty({
    description: 'Asset information',
    type: Object,
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      name: 'Office Laptop',
      assetCode: 'AST-001',
    },
  })
  asset: {
    id: string;
    name: string;
    assetCode?: string;
  };

  @ApiProperty({
    description: 'Type of adjustment',
    enum: AssetAdjustmentType,
    enumName: 'AssetAdjustmentType',
    example: AssetAdjustmentType.DEPRECIATION,
  })
  adjustmentType: AssetAdjustmentType;

  @ApiProperty({
    description: 'Date when the adjustment is effective',
    example: '2024-01-15',
  })
  adjustmentDate: string;

  @ApiPropertyOptional({
    description: 'Effective period for depreciation (YYYY-MM format)',
    example: '2024-01',
    nullable: true,
  })
  effectivePeriod?: string;

  @ApiProperty({
    description: 'Adjustment amount',
    example: '1500.00',
  })
  amount: string;

  @ApiProperty({
    description: 'Book value before adjustment',
    example: '10000.00',
  })
  bookValueBefore: string;

  @ApiProperty({
    description: 'Book value after adjustment',
    example: '8500.00',
  })
  bookValueAfter: string;

  @ApiPropertyOptional({
    description: 'Description of the adjustment',
    nullable: true,
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Reference number for the adjustment',
    nullable: true,
  })
  referenceNo?: string;

  @ApiProperty({
    description: 'Status of the adjustment',
    example: 'posted',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Journal entry ID if linked to accounting',
    nullable: true,
  })
  journalEntryId?: string;

  @ApiPropertyOptional({
    description: 'Journal entry information if linked',
    type: Object,
    nullable: true,
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      entryNumber: 'JE-001',
      description: 'Asset depreciation adjustment',
    },
  })
  journalEntry?: {
    id: string;
    entryNumber: string;
    description?: string;
  };
}
