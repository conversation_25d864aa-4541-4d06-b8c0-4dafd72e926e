import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsEnum,
  IsDecimal,
  IsDateString,
  Matches,
} from 'class-validator';
import { AssetAdjustmentType } from '../../drizzle/schema/asset-adjustments.schema';

export class UpdateAssetAdjustmentDto {
  @ApiPropertyOptional({ description: 'Asset ID to adjust' })
  @IsOptional()
  @IsUUID()
  assetId?: string;

  @ApiPropertyOptional({
    description: 'Type of adjustment',
    enum: AssetAdjustmentType,
    enumName: 'AssetAdjustmentType',
  })
  @IsOptional()
  @IsEnum(AssetAdjustmentType, {
    message: 'Adjustment type must be a valid asset adjustment type',
  })
  adjustmentType?: AssetAdjustmentType;

  @ApiPropertyOptional({
    description: 'Date when the adjustment is effective',
    example: '2024-01-15',
  })
  @IsOptional()
  @IsDateString()
  adjustmentDate?: string;

  @ApiPropertyOptional({
    description: 'Effective period for depreciation (YYYY-MM format)',
    example: '2024-01',
    maxLength: 7,
  })
  @IsOptional()
  @IsString()
  @MaxLength(7)
  @Matches(/^\d{4}-\d{2}$/, {
    message: 'Effective period must be in YYYY-MM format',
  })
  effectivePeriod?: string;

  @ApiPropertyOptional({
    description: 'Adjustment amount',
    example: '1500.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' }, {
    message: 'Amount must be a valid decimal with up to 2 decimal places',
  })
  amount?: string;

  @ApiPropertyOptional({
    description: 'Book value before adjustment',
    example: '10000.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' }, {
    message: 'Book value before must be a valid decimal with up to 2 decimal places',
  })
  bookValueBefore?: string;

  @ApiPropertyOptional({
    description: 'Book value after adjustment',
    example: '8500.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' }, {
    message: 'Book value after must be a valid decimal with up to 2 decimal places',
  })
  bookValueAfter?: string;

  @ApiPropertyOptional({
    description: 'Description of the adjustment',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Reference number for the adjustment',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  referenceNo?: string;

  @ApiPropertyOptional({
    description: 'Status of the adjustment',
    maxLength: 20,
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  status?: string;

  @ApiPropertyOptional({
    description: 'Journal entry ID if linked to accounting',
  })
  @IsOptional()
  @IsUUID()
  journalEntryId?: string;
}
