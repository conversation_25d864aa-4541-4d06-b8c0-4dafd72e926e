import { ApiProperty } from '@nestjs/swagger';
import { AssetAdjustmentListDto } from './asset-adjustment-list.dto';

export class PaginationMetaDto {
  @ApiProperty({ description: 'Total number of records', example: 100 })
  total: number;

  @ApiProperty({ description: 'Current page number', example: 1 })
  page: number;

  @ApiProperty({ description: 'Total number of pages', example: 10 })
  totalPages: number;
}

export class PaginatedAssetAdjustmentsResponseDto {
  @ApiProperty({
    description: 'Array of asset adjustments',
    type: [AssetAdjustmentListDto],
  })
  data: AssetAdjustmentListDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: PaginationMetaDto,
  })
  meta: PaginationMetaDto;
}
