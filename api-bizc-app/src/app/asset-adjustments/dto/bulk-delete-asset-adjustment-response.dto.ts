import { ApiProperty } from '@nestjs/swagger';

export class BulkDeleteAssetAdjustmentResponseDto {
  @ApiProperty({
    description: 'Number of asset adjustments deleted',
    example: 5,
  })
  deletedCount: number;

  @ApiProperty({
    description: 'Array of deleted asset adjustment IDs',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-446655440000',
      '550e8400-e29b-41d4-a716-446655440001',
    ],
  })
  deletedIds: string[];
}
