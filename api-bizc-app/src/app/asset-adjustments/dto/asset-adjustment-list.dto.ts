import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AssetAdjustmentType } from '../../drizzle/schema/asset-adjustments.schema';

export class AssetAdjustmentListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Asset adjustment ID',
  })
  id: string;

  @ApiProperty({
    description: 'Asset information',
    type: Object,
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'Office Laptop',
      assetCode: 'AST-001',
    },
  })
  asset: {
    id: string;
    name: string;
    assetCode?: string;
  };

  @ApiProperty({
    description: 'Type of adjustment',
    enum: AssetAdjustmentType,
    enumName: 'AssetAdjustmentType',
    example: AssetAdjustmentType.DEPRECIATION,
  })
  adjustmentType: AssetAdjustmentType;

  @ApiProperty({
    description: 'Date when the adjustment is effective',
    example: '2024-01-15',
  })
  adjustmentDate: string;

  @ApiPropertyOptional({
    description: 'Effective period for depreciation (YYYY-MM format)',
    example: '2024-01',
    nullable: true,
  })
  effectivePeriod?: string;

  @ApiProperty({
    description: 'Adjustment amount',
    example: '1500.00',
  })
  amount: string;

  @ApiProperty({
    description: 'Book value before adjustment',
    example: '10000.00',
  })
  bookValueBefore: string;

  @ApiProperty({
    description: 'Book value after adjustment',
    example: '8500.00',
  })
  bookValueAfter: string;

  @ApiProperty({
    description: 'Status of the adjustment',
    example: 'posted',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Reference number for the adjustment',
    nullable: true,
  })
  referenceNo?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-12-01T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-12-01T15:45:00.000Z',
  })
  updatedAt: Date;
}
