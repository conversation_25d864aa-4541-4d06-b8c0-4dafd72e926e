import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class BulkDeleteAssetAdjustmentDto {
  @ApiProperty({
    description: 'Array of asset adjustment IDs to delete',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
  })
  @IsArray({ message: 'IDs must be an array' })
  @IsUUID('4', { each: true, message: 'Each ID must be a valid UUID' })
  ids: string[];
}
