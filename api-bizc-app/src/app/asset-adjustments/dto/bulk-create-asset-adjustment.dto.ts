import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsArray } from 'class-validator';
import { Transform } from 'class-transformer';
import { CreateAssetAdjustmentDto } from './create-asset-adjustment.dto';

export class BulkCreateAssetAdjustmentDto {
  @ApiProperty({
    description: 'JSON string containing array of asset adjustment objects',
    example:
      '[{"assetId":"123e4567-e89b-12d3-a456-426614174000","adjustmentType":"depreciation","adjustmentDate":"2024-01-15","amount":"1500.00","bookValueBefore":"10000.00","bookValueAfter":"8500.00"},{"assetId":"123e4567-e89b-12d3-a456-426614174001","adjustmentType":"revaluation","adjustmentDate":"2024-01-16","amount":"2000.00","bookValueBefore":"15000.00","bookValueAfter":"17000.00"}]',
  })
  @IsNotEmpty({ message: 'Asset adjustments are required' })
  @Transform(({ value }) => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return value;
    }
  })
  @IsArray({ message: 'Asset adjustments must be an array' })
  assetAdjustments: CreateAssetAdjustmentDto[];
}
