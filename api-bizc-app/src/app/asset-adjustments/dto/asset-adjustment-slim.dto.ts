import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AssetAdjustmentType } from '../../drizzle/schema/asset-adjustments.schema';

export class AssetAdjustmentSlimDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Asset adjustment ID',
  })
  id: string;

  @ApiProperty({
    description: 'Asset information',
    type: Object,
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'Office Laptop',
      assetCode: 'AST-001',
    },
  })
  asset: {
    id: string;
    name: string;
    assetCode?: string;
  };

  @ApiProperty({
    description: 'Type of adjustment',
    enum: AssetAdjustmentType,
    enumName: 'AssetAdjustmentType',
    example: AssetAdjustmentType.DEPRECIATION,
  })
  adjustmentType: AssetAdjustmentType;

  @ApiProperty({
    description: 'Date when the adjustment is effective',
    example: '2024-01-15',
  })
  adjustmentDate: string;

  @ApiProperty({
    description: 'Adjustment amount',
    example: '1500.00',
  })
  amount: string;

  @ApiProperty({
    description: 'Status of the adjustment',
    example: 'posted',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Reference number for the adjustment',
    nullable: true,
  })
  referenceNo?: string;
}
