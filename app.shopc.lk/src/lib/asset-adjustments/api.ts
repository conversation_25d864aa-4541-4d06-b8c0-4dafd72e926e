import {
  AssetAdjustmentResponse,
  AssetAdjustmentPaginatedResponse,
  AssetAdjustmentIdResponse,
  BulkAssetAdjustmentIdsResponse,
  BulkDeleteAssetAdjustmentDto,
  BulkDeleteAssetAdjustmentResponse,
  SimpleAssetAdjustmentResponse,
  CreateAssetAdjustmentDto,
  UpdateAssetAdjustmentDto,
  BulkCreateAssetAdjustmentDto,
} from "@/types/asset-adjustment";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetAssetAdjustmentsSchema } from "./validations";
import axios from "@/utils/axios";

// Create a new asset adjustment
export async function createAssetAdjustmentApi(
  data: CreateAssetAdjustmentDto
): Promise<AssetAdjustmentIdResponse> {
  try {
    const res = await axios.post("/asset-adjustments", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error?.data || "Something went wrong",
      data: null,
    };
  }
}

// Bulk create asset adjustments
export async function bulkCreateAssetAdjustmentsApi(
  assetAdjustments: BulkCreateAssetAdjustmentDto[]
): Promise<BulkAssetAdjustmentIdsResponse> {
  try {
    const res = await axios.post("/asset-adjustments/bulk", {
      assetAdjustments,
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error?.data || "Something went wrong",
      data: null,
    };
  }
}

// Get asset adjustments with pagination and filters
export async function getAssetAdjustmentsApi(
  params: GetAssetAdjustmentsSchema
): Promise<AssetAdjustmentPaginatedResponse> {
  try {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append("page", params.page.toString());
    if (params.limit) queryParams.append("limit", params.limit.toString());
    if (params.from) queryParams.append("from", params.from);
    if (params.to) queryParams.append("to", params.to);
    if (params.assetId) queryParams.append("assetId", params.assetId);
    if (params.adjustmentType) queryParams.append("adjustmentType", params.adjustmentType);
    if (params.status) queryParams.append("status", params.status);
    if (params.referenceNo) queryParams.append("referenceNo", params.referenceNo);
    if (params.sort) queryParams.append("sort", params.sort);

    const res = await axios.get(`/asset-adjustments?${queryParams.toString()}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error?.data || "Something went wrong",
      data: null,
    };
  }
}

// Get asset adjustments in slim format (for dropdowns/selects)
export async function getAssetAdjustmentsSlimApi(): Promise<SimpleAssetAdjustmentResponse> {
  try {
    const res = await axios.get("/asset-adjustments/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error?.data || "Something went wrong",
      data: null,
    };
  }
}

// Get a single asset adjustment by ID
export async function getAssetAdjustmentApi(id: string): Promise<AssetAdjustmentResponse> {
  try {
    const res = await axios.get(`/asset-adjustments/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error?.data || "Something went wrong",
      data: null,
    };
  }
}

// Update an asset adjustment
export async function updateAssetAdjustmentApi(
  id: string,
  data: UpdateAssetAdjustmentDto
): Promise<AssetAdjustmentResponse> {
  try {
    const res = await axios.patch(`/asset-adjustments/${id}`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error?.data || "Something went wrong",
      data: null,
    };
  }
}

// Delete an asset adjustment
export async function deleteAssetAdjustmentApi(
  id: string
): Promise<ApiResponse<{ message: string; id: string }>> {
  try {
    const res = await axios.delete(`/asset-adjustments/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error?.data || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete asset adjustments
export async function bulkDeleteAssetAdjustmentsApi(
  data: BulkDeleteAssetAdjustmentDto
): Promise<BulkDeleteAssetAdjustmentResponse> {
  try {
    const res = await axios.delete("/asset-adjustments/bulk", {
      data,
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error?.data || "Something went wrong",
      data: null,
    };
  }
}
