import {
  AssetAdjustmentPaginatedResponse,
  AssetAdjustmentIdResponse,
  BulkAssetAdjustmentIdsResponse,
  BulkDeleteAssetAdjustmentResponse,
  SimpleAssetAdjustmentResponse,
  AssetAdjustmentResponse,
  CreateAssetAdjustmentDto,
  UpdateAssetAdjustmentDto,
  BulkCreateAssetAdjustmentDto,
  BulkDeleteAssetAdjustmentDto,
  PaginatedAssetAdjustmentsResponseDto,
} from "@/types/asset-adjustment";
import { ApiResponse } from "@/types/common";
import { GetAssetAdjustmentsSchema } from "./validations";

// Real API imports
import {
  createAssetAdjustmentApi,
  bulkCreateAssetAdjustmentsApi,
  getAssetAdjustmentsApi,
  getAssetAdjustmentsSlimApi,
  getAssetAdjustmentApi,
  updateAssetAdjustmentApi,
  deleteAssetAdjustmentApi,
  bulkDeleteAssetAdjustmentsApi,
} from "./api";

// Demo API imports
import {
  getDemoAssetAdjustmentsTableDataApi,
  getDemoAssetAdjustmentsSlimApi,
  getDemoAssetAdjustmentApi,
  createDemoAssetAdjustmentApi,
  bulkCreateDemoAssetAdjustmentsApi,
  updateDemoAssetAdjustmentApi,
  deleteDemoAssetAdjustmentApi,
  bulkDeleteDemoAssetAdjustmentsApi,
} from "./demo";

// Get asset adjustments table data with pagination and filters
export async function getAssetAdjustmentsTableData(
  params: GetAssetAdjustmentsSchema,
  isDemo: boolean
): Promise<PaginatedAssetAdjustmentsResponseDto> {
  if (isDemo) {
    return await getDemoAssetAdjustmentsTableDataApi(params);
  } else {
    const response = await getAssetAdjustmentsApi(params);
    return response.data || { data: [], meta: { total: 0, page: 1, totalPages: 0 } };
  }
}

// Get asset adjustments in slim format (for dropdowns/selects)
export async function getAssetAdjustmentsSlim(
  isDemo: boolean
): Promise<SimpleAssetAdjustmentResponse> {
  if (isDemo) {
    return await getDemoAssetAdjustmentsSlimApi();
  } else {
    return await getAssetAdjustmentsSlimApi();
  }
}

// Get a single asset adjustment by ID
export async function getAssetAdjustment(
  id: string,
  isDemo: boolean
): Promise<AssetAdjustmentResponse> {
  if (isDemo) {
    return await getDemoAssetAdjustmentApi(id);
  } else {
    return await getAssetAdjustmentApi(id);
  }
}

// Create a new asset adjustment
export async function createAssetAdjustment(
  data: CreateAssetAdjustmentDto,
  isDemo: boolean
): Promise<AssetAdjustmentIdResponse> {
  if (isDemo) {
    return await createDemoAssetAdjustmentApi(data);
  } else {
    return await createAssetAdjustmentApi(data);
  }
}

// Bulk create asset adjustments
export async function bulkCreateAssetAdjustments(
  assetAdjustments: BulkCreateAssetAdjustmentDto[],
  isDemo: boolean
): Promise<BulkAssetAdjustmentIdsResponse> {
  if (isDemo) {
    return await bulkCreateDemoAssetAdjustmentsApi(assetAdjustments);
  } else {
    return await bulkCreateAssetAdjustmentsApi(assetAdjustments);
  }
}

// Update an asset adjustment
export async function updateAssetAdjustment(
  id: string,
  data: UpdateAssetAdjustmentDto,
  isDemo: boolean
): Promise<AssetAdjustmentResponse> {
  if (isDemo) {
    return await updateDemoAssetAdjustmentApi(id, data);
  } else {
    return await updateAssetAdjustmentApi(id, data);
  }
}

// Delete an asset adjustment
export async function deleteAssetAdjustment(
  id: string,
  isDemo: boolean
): Promise<ApiResponse<{ message: string; id: string }>> {
  if (isDemo) {
    return await deleteDemoAssetAdjustmentApi(id);
  } else {
    return await deleteAssetAdjustmentApi(id);
  }
}

// Bulk delete asset adjustments
export async function bulkDeleteAssetAdjustments(
  data: BulkDeleteAssetAdjustmentDto,
  isDemo: boolean
): Promise<BulkDeleteAssetAdjustmentResponse> {
  if (isDemo) {
    return await bulkDeleteDemoAssetAdjustmentsApi(data);
  } else {
    return await bulkDeleteAssetAdjustmentsApi(data);
  }
}
