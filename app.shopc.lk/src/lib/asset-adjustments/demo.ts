import {
  AssetAdjustmentDto,
  AssetAdjustmentSlimDto,
  AssetAdjustmentListDto,
  AssetAdjustmentType,
  AssetAdjustmentPaginatedResponse,
  AssetAdjustmentIdResponse,
  BulkAssetAdjustmentIdsResponse,
  BulkDeleteAssetAdjustmentResponse,
  SimpleAssetAdjustmentResponse,
  AssetAdjustmentResponse,
  CreateAssetAdjustmentDto,
  UpdateAssetAdjustmentDto,
  BulkCreateAssetAdjustmentDto,
  BulkDeleteAssetAdjustmentDto,
  PaginatedAssetAdjustmentsResponseDto,
} from "@/types/asset-adjustment";
import { ApiStatus, ApiResponse } from "@/types/common";
import { GetAssetAdjustmentsSchema } from "./validations";

// Demo assets data for reference
const demoAssets = [
  { id: "asset_1", name: "Office Laptop Dell XPS 13", assetCode: "AST-001" },
  { id: "asset_2", name: "Conference Room Projector", assetCode: "AST-002" },
  { id: "asset_3", name: "Company Vehicle Toyota Camry", assetCode: "AST-003" },
  { id: "asset_4", name: "Office Printer HP LaserJet", assetCode: "AST-004" },
  { id: "asset_5", name: "Server Dell PowerEdge", assetCode: "AST-005" },
];

// Raw demo asset adjustments data
const rawDemoAssetAdjustments = [
  {
    id: "adj_1",
    businessId: "biz_1",
    assetId: "asset_1",
    adjustmentType: AssetAdjustmentType.DEPRECIATION,
    adjustmentDate: "2024-01-15",
    effectivePeriod: "2024-01",
    amount: "150.00",
    bookValueBefore: "2000.00",
    bookValueAfter: "1850.00",
    description: "Monthly depreciation for office laptop",
    referenceNo: "DEP-2024-001",
    status: "posted",
    journalEntryId: "je_1",
    createdBy: "user_1",
    updatedBy: "user_1",
    createdAt: new Date("2024-01-15T10:00:00Z"),
    updatedAt: new Date("2024-01-15T10:00:00Z"),
  },
  {
    id: "adj_2",
    businessId: "biz_1",
    assetId: "asset_2",
    adjustmentType: AssetAdjustmentType.REVALUATION,
    adjustmentDate: "2024-01-20",
    amount: "500.00",
    bookValueBefore: "3000.00",
    bookValueAfter: "3500.00",
    description: "Revaluation due to market price increase",
    referenceNo: "REV-2024-001",
    status: "posted",
    createdBy: "user_1",
    updatedBy: "user_1",
    createdAt: new Date("2024-01-20T14:30:00Z"),
    updatedAt: new Date("2024-01-20T14:30:00Z"),
  },
  {
    id: "adj_3",
    businessId: "biz_1",
    assetId: "asset_3",
    adjustmentType: AssetAdjustmentType.IMPAIRMENT,
    adjustmentDate: "2024-02-01",
    amount: "2000.00",
    bookValueBefore: "25000.00",
    bookValueAfter: "23000.00",
    description: "Impairment due to accident damage",
    referenceNo: "IMP-2024-001",
    status: "posted",
    createdBy: "user_2",
    updatedBy: "user_2",
    createdAt: new Date("2024-02-01T09:15:00Z"),
    updatedAt: new Date("2024-02-01T09:15:00Z"),
  },
  {
    id: "adj_4",
    businessId: "biz_1",
    assetId: "asset_4",
    adjustmentType: AssetAdjustmentType.CORRECTION,
    adjustmentDate: "2024-02-05",
    amount: "100.00",
    bookValueBefore: "800.00",
    bookValueAfter: "900.00",
    description: "Correction of initial valuation error",
    referenceNo: "COR-2024-001",
    status: "posted",
    createdBy: "user_1",
    updatedBy: "user_1",
    createdAt: new Date("2024-02-05T16:45:00Z"),
    updatedAt: new Date("2024-02-05T16:45:00Z"),
  },
  {
    id: "adj_5",
    businessId: "biz_1",
    assetId: "asset_5",
    adjustmentType: AssetAdjustmentType.DEPRECIATION,
    adjustmentDate: "2024-02-15",
    effectivePeriod: "2024-02",
    amount: "300.00",
    bookValueBefore: "15000.00",
    bookValueAfter: "14700.00",
    description: "Monthly depreciation for server equipment",
    referenceNo: "DEP-2024-002",
    status: "posted",
    createdBy: "user_2",
    updatedBy: "user_2",
    createdAt: new Date("2024-02-15T11:20:00Z"),
    updatedAt: new Date("2024-02-15T11:20:00Z"),
  },
];

// Function to get processed demo asset adjustments with asset details
function getProcessedDemoAssetAdjustments(): AssetAdjustmentDto[] {
  return rawDemoAssetAdjustments.map((adjustment) => {
    const asset = demoAssets.find((a) => a.id === adjustment.assetId);
    return {
      ...adjustment,
      asset: asset || {
        id: adjustment.assetId,
        name: "Unknown Asset",
        assetCode: "N/A",
      },
      journalEntry: adjustment.journalEntryId
        ? {
            id: adjustment.journalEntryId,
            entryNumber: `JE-${adjustment.id.split("_")[1].padStart(3, "0")}`,
            description: `Journal entry for ${adjustment.description}`,
          }
        : undefined,
    };
  });
}

// Export demo asset adjustments
export let demoAssetAdjustments: AssetAdjustmentDto[] =
  getProcessedDemoAssetAdjustments();

// Convert to table format
function convertToTableFormat(
  adjustments: AssetAdjustmentDto[]
): AssetAdjustmentListDto[] {
  return adjustments.map((adjustment) => ({
    id: adjustment.id,
    asset: adjustment.asset,
    adjustmentType: adjustment.adjustmentType,
    adjustmentDate: adjustment.adjustmentDate,
    effectivePeriod: adjustment.effectivePeriod,
    amount: adjustment.amount,
    bookValueBefore: adjustment.bookValueBefore,
    bookValueAfter: adjustment.bookValueAfter,
    status: adjustment.status,
    referenceNo: adjustment.referenceNo,
    createdAt: adjustment.createdAt,
    updatedAt: adjustment.updatedAt,
  }));
}

// Convert to slim format
function convertToSlimFormat(
  adjustments: AssetAdjustmentDto[]
): AssetAdjustmentSlimDto[] {
  return adjustments.map((adjustment) => ({
    id: adjustment.id,
    asset: adjustment.asset,
    adjustmentType: adjustment.adjustmentType,
    adjustmentDate: adjustment.adjustmentDate,
    amount: adjustment.amount,
  }));
}

// Demo API functions
export async function getDemoAssetAdjustmentsTableDataApi(
  params: GetAssetAdjustmentsSchema
): Promise<PaginatedAssetAdjustmentsResponseDto> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  let filteredAdjustments = [...demoAssetAdjustments];

  // Apply filters
  if (params.assetId) {
    filteredAdjustments = filteredAdjustments.filter(
      (adj) => adj.assetId === params.assetId
    );
  }

  if (params.adjustmentType) {
    filteredAdjustments = filteredAdjustments.filter(
      (adj) => adj.adjustmentType === params.adjustmentType
    );
  }

  if (params.status) {
    filteredAdjustments = filteredAdjustments.filter((adj) =>
      adj.status.toLowerCase().includes(params.status!.toLowerCase())
    );
  }

  if (params.referenceNo) {
    filteredAdjustments = filteredAdjustments.filter(
      (adj) =>
        adj.referenceNo &&
        adj.referenceNo
          .toLowerCase()
          .includes(params.referenceNo!.toLowerCase())
    );
  }

  if (params.from) {
    filteredAdjustments = filteredAdjustments.filter(
      (adj) => adj.adjustmentDate >= params.from!
    );
  }

  if (params.to) {
    filteredAdjustments = filteredAdjustments.filter(
      (adj) => adj.adjustmentDate <= params.to!
    );
  }

  // Apply sorting
  if (params.sort) {
    const [field, direction] = params.sort.split(":");
    filteredAdjustments.sort((a, b) => {
      let aValue: any = a[field as keyof AssetAdjustmentDto];
      let bValue: any = b[field as keyof AssetAdjustmentDto];

      if (field === "assetName") {
        aValue = a.asset.name;
        bValue = b.asset.name;
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        return direction === "desc"
          ? bValue.localeCompare(aValue)
          : aValue.localeCompare(bValue);
      }

      if (aValue instanceof Date && bValue instanceof Date) {
        return direction === "desc"
          ? bValue.getTime() - aValue.getTime()
          : aValue.getTime() - bValue.getTime();
      }

      return direction === "desc" ? bValue - aValue : aValue - bValue;
    });
  } else {
    // Default sort by creation date (newest first)
    filteredAdjustments.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
    );
  }

  // Apply pagination
  const page = params.page || 1;
  const limit = params.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedAdjustments = filteredAdjustments.slice(startIndex, endIndex);

  return {
    data: convertToTableFormat(paginatedAdjustments),
    meta: {
      total: filteredAdjustments.length,
      page,
      totalPages: Math.ceil(filteredAdjustments.length / limit),
    },
  };
}

export async function getDemoAssetAdjustmentsSlimApi(): Promise<SimpleAssetAdjustmentResponse> {
  await new Promise((resolve) => setTimeout(resolve, 200));

  return {
    status: ApiStatus.SUCCESS,
    message: null,
    data: convertToSlimFormat(demoAssetAdjustments),
  };
}

export async function getDemoAssetAdjustmentApi(
  id: string
): Promise<AssetAdjustmentResponse> {
  await new Promise((resolve) => setTimeout(resolve, 200));

  const adjustment = demoAssetAdjustments.find((adj) => adj.id === id);

  if (!adjustment) {
    return {
      status: ApiStatus.FAIL,
      message: "Asset adjustment not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: null,
    data: adjustment,
  };
}

export async function createDemoAssetAdjustmentApi(
  data: CreateAssetAdjustmentDto
): Promise<AssetAdjustmentIdResponse> {
  await new Promise((resolve) => setTimeout(resolve, 500));

  const newId = `adj_${Date.now()}`;
  const asset = demoAssets.find((a) => a.id === data.assetId);

  if (!asset) {
    return {
      status: ApiStatus.FAIL,
      message: "Asset not found",
      data: null,
    };
  }

  const newAdjustment: AssetAdjustmentDto = {
    id: newId,
    businessId: "demo-business-id",
    assetId: data.assetId,
    asset,
    adjustmentType: data.adjustmentType,
    adjustmentDate: data.adjustmentDate,
    effectivePeriod: data.effectivePeriod,
    amount: data.amount,
    bookValueBefore: data.bookValueBefore,
    bookValueAfter: data.bookValueAfter,
    description: data.description,
    referenceNo: data.referenceNo,
    status: data.status || "posted",
    journalEntryId: data.journalEntryId,
    journalEntry: data.journalEntryId
      ? {
          id: data.journalEntryId,
          entryNumber: `JE-${newId.split("_")[1]}`,
          description: `Journal entry for ${data.description}`,
        }
      : undefined,
    createdBy: "demo-user-1",
    updatedBy: "demo-user-1",
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  demoAssetAdjustments.unshift(newAdjustment);

  return {
    status: ApiStatus.SUCCESS,
    message: "Asset adjustment created successfully",
    data: { id: newId },
  };
}

export async function bulkCreateDemoAssetAdjustmentsApi(
  assetAdjustments: BulkCreateAssetAdjustmentDto[]
): Promise<BulkAssetAdjustmentIdsResponse> {
  await new Promise((resolve) => setTimeout(resolve, 800));

  const createdIds: string[] = [];

  for (const adjustmentData of assetAdjustments) {
    const newId = `adj_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    const asset = demoAssets.find((a) => a.id === adjustmentData.assetId);

    if (asset) {
      const newAdjustment: AssetAdjustmentDto = {
        id: newId,
        businessId: "demo-business-id",
        assetId: adjustmentData.assetId,
        asset,
        adjustmentType: adjustmentData.adjustmentType,
        adjustmentDate: adjustmentData.adjustmentDate,
        effectivePeriod: adjustmentData.effectivePeriod,
        amount: adjustmentData.amount,
        bookValueBefore: adjustmentData.bookValueBefore,
        bookValueAfter: adjustmentData.bookValueAfter,
        description: adjustmentData.description,
        referenceNo: adjustmentData.referenceNo,
        status: adjustmentData.status || "posted",
        journalEntryId: adjustmentData.journalEntryId,
        journalEntry: adjustmentData.journalEntryId
          ? {
              id: adjustmentData.journalEntryId,
              entryNumber: `JE-${newId.split("_")[1]}`,
              description: `Journal entry for ${adjustmentData.description}`,
            }
          : undefined,
        createdBy: "demo-user-1",
        updatedBy: "demo-user-1",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      demoAssetAdjustments.unshift(newAdjustment);
      createdIds.push(newId);
    }
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${createdIds.length} asset adjustments created successfully`,
    data: { ids: createdIds },
  };
}

export async function updateDemoAssetAdjustmentApi(
  id: string,
  data: UpdateAssetAdjustmentDto
): Promise<AssetAdjustmentResponse> {
  await new Promise((resolve) => setTimeout(resolve, 400));

  const adjustmentIndex = demoAssetAdjustments.findIndex(
    (adj) => adj.id === id
  );

  if (adjustmentIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Asset adjustment not found",
      data: null,
    };
  }

  const existingAdjustment = demoAssetAdjustments[adjustmentIndex];
  let asset = existingAdjustment.asset;

  // If assetId is being updated, find the new asset
  if (data.assetId && data.assetId !== existingAdjustment.assetId) {
    const newAsset = demoAssets.find((a) => a.id === data.assetId);
    if (!newAsset) {
      return {
        status: ApiStatus.FAIL,
        message: "Asset not found",
        data: null,
      };
    }
    asset = newAsset;
  }

  const updatedAdjustment: AssetAdjustmentDto = {
    ...existingAdjustment,
    ...data,
    asset,
    journalEntry: data.journalEntryId
      ? {
          id: data.journalEntryId,
          entryNumber: `JE-${id.split("_")[1]}`,
          description: `Journal entry for ${
            data.description || existingAdjustment.description
          }`,
        }
      : existingAdjustment.journalEntry,
    updatedBy: "demo-user-1",
    updatedAt: new Date(),
  };

  demoAssetAdjustments[adjustmentIndex] = updatedAdjustment;

  return {
    status: ApiStatus.SUCCESS,
    message: "Asset adjustment updated successfully",
    data: updatedAdjustment,
  };
}

export async function deleteDemoAssetAdjustmentApi(
  id: string
): Promise<ApiResponse<{ message: string; id: string }>> {
  await new Promise((resolve) => setTimeout(resolve, 300));

  const adjustmentIndex = demoAssetAdjustments.findIndex(
    (adj) => adj.id === id
  );

  if (adjustmentIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Asset adjustment not found",
      data: null,
    };
  }

  demoAssetAdjustments.splice(adjustmentIndex, 1);

  return {
    status: ApiStatus.SUCCESS,
    message: "Asset adjustment deleted successfully",
    data: { message: "Asset adjustment deleted successfully", id },
  };
}

export async function bulkDeleteDemoAssetAdjustmentsApi(
  data: BulkDeleteAssetAdjustmentDto
): Promise<BulkDeleteAssetAdjustmentResponse> {
  await new Promise((resolve) => setTimeout(resolve, 500));

  const deletedIds: string[] = [];

  for (const id of data.ids) {
    const adjustmentIndex = demoAssetAdjustments.findIndex(
      (adj) => adj.id === id
    );
    if (adjustmentIndex !== -1) {
      demoAssetAdjustments.splice(adjustmentIndex, 1);
      deletedIds.push(id);
    }
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${deletedIds.length} asset adjustments deleted successfully`,
    data: {
      deletedCount: deletedIds.length,
      deletedIds,
    },
  };
}
