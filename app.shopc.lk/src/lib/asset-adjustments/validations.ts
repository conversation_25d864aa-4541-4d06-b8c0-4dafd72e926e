import { z } from "zod";
import { AssetAdjustmentType } from "@/types/asset-adjustment";

// Backend DTO: CreateAssetAdjustmentDto
export const createAssetAdjustmentSchema = z.object({
  assetId: z.string().uuid("Invalid asset ID"),
  adjustmentType: z.nativeEnum(AssetAdjustmentType, {
    errorMap: () => ({ message: "Invalid adjustment type" }),
  }),
  adjustmentDate: z.string().min(1, "Adjustment date is required"),
  effectivePeriod: z
    .string()
    .regex(/^\d{4}-\d{2}$/, "Effective period must be in YYYY-MM format")
    .optional(),
  amount: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Amount must be a valid decimal with up to 2 decimal places")
    .min(1, "Amount is required"),
  bookValueBefore: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Book value before must be a valid decimal with up to 2 decimal places")
    .min(1, "Book value before is required"),
  bookValueAfter: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Book value after must be a valid decimal with up to 2 decimal places")
    .min(1, "Book value after is required"),
  description: z.string().optional(),
  referenceNo: z.string().max(100, "Reference number must be at most 100 characters").optional(),
  status: z.string().max(20, "Status must be at most 20 characters").optional(),
  journalEntryId: z.string().uuid("Invalid journal entry ID").optional(),
});

// Backend DTO: UpdateAssetAdjustmentDto
export const updateAssetAdjustmentSchema = z.object({
  assetId: z.string().uuid("Invalid asset ID").optional(),
  adjustmentType: z.nativeEnum(AssetAdjustmentType, {
    errorMap: () => ({ message: "Invalid adjustment type" }),
  }).optional(),
  adjustmentDate: z.string().min(1, "Adjustment date is required").optional(),
  effectivePeriod: z
    .string()
    .regex(/^\d{4}-\d{2}$/, "Effective period must be in YYYY-MM format")
    .optional(),
  amount: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Amount must be a valid decimal with up to 2 decimal places")
    .optional(),
  bookValueBefore: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Book value before must be a valid decimal with up to 2 decimal places")
    .optional(),
  bookValueAfter: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Book value after must be a valid decimal with up to 2 decimal places")
    .optional(),
  description: z.string().optional(),
  referenceNo: z.string().max(100, "Reference number must be at most 100 characters").optional(),
  status: z.string().max(20, "Status must be at most 20 characters").optional(),
  journalEntryId: z.string().uuid("Invalid journal entry ID").optional(),
});

// Query parameters for getting asset adjustments
export const getAssetAdjustmentsSchema = z.object({
  page: z.number().int().min(1, "Page must be at least 1").optional(),
  limit: z.number().int().min(1, "Limit must be at least 1").max(100, "Limit must be at most 100").optional(),
  from: z.string().optional(), // Date string for filtering from date
  to: z.string().optional(), // Date string for filtering to date
  assetId: z.string().uuid("Invalid asset ID").optional(),
  adjustmentType: z.nativeEnum(AssetAdjustmentType).optional(),
  status: z.string().optional(),
  referenceNo: z.string().optional(),
  sort: z.string().optional(), // Sort parameter
});

// Bulk create asset adjustments
export const bulkCreateAssetAdjustmentsSchema = z.object({
  assetAdjustments: z.array(createAssetAdjustmentSchema).min(1, "At least one asset adjustment is required"),
});

// Bulk delete asset adjustments
export const bulkDeleteAssetAdjustmentsSchema = z.object({
  ids: z.array(z.string().uuid("Invalid asset adjustment ID")).min(1, "At least one ID is required"),
});

// Form schema for UI components (includes additional fields for form handling)
export const assetAdjustmentFormSchema = z.object({
  assetId: z.string().uuid("Invalid asset ID"),
  adjustmentType: z.nativeEnum(AssetAdjustmentType, {
    errorMap: () => ({ message: "Adjustment type is required" }),
  }),
  adjustmentDate: z.string().min(1, "Adjustment date is required"),
  effectivePeriod: z
    .string()
    .regex(/^\d{4}-\d{2}$/, "Effective period must be in YYYY-MM format")
    .optional(),
  amount: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Amount must be a valid decimal with up to 2 decimal places")
    .min(1, "Amount is required"),
  bookValueBefore: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Book value before must be a valid decimal with up to 2 decimal places")
    .min(1, "Book value before is required"),
  bookValueAfter: z
    .string()
    .regex(/^\d+(\.\d{1,2})?$/, "Book value after must be a valid decimal with up to 2 decimal places")
    .min(1, "Book value after is required"),
  description: z.string().optional(),
  referenceNo: z.string().max(100, "Reference number must be at most 100 characters").optional(),
  status: z.string().max(20, "Status must be at most 20 characters").optional(),
  journalEntryId: z.string().uuid("Invalid journal entry ID").optional(),
}).superRefine((data, ctx) => {
  // Custom validation: book value after should be different from book value before
  if (data.bookValueBefore === data.bookValueAfter) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Book value after must be different from book value before",
      path: ["bookValueAfter"],
    });
  }

  // Custom validation: amount should match the difference between book values for certain adjustment types
  const bookValueBefore = parseFloat(data.bookValueBefore);
  const bookValueAfter = parseFloat(data.bookValueAfter);
  const amount = parseFloat(data.amount);
  const difference = Math.abs(bookValueBefore - bookValueAfter);

  if (!isNaN(bookValueBefore) && !isNaN(bookValueAfter) && !isNaN(amount)) {
    if (Math.abs(difference - amount) > 0.01) { // Allow for small floating point differences
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Amount should match the difference between book values",
        path: ["amount"],
      });
    }
  }

  // Custom validation: effective period is required for depreciation adjustments
  if (data.adjustmentType === AssetAdjustmentType.DEPRECIATION && !data.effectivePeriod) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Effective period is required for depreciation adjustments",
      path: ["effectivePeriod"],
    });
  }
});

// Export types for TypeScript inference
export type GetAssetAdjustmentsSchema = z.infer<typeof getAssetAdjustmentsSchema>;
export type CreateAssetAdjustmentSchema = z.infer<typeof createAssetAdjustmentSchema>;
export type UpdateAssetAdjustmentSchema = z.infer<typeof updateAssetAdjustmentSchema>;
export type BulkCreateAssetAdjustmentsSchema = z.infer<typeof bulkCreateAssetAdjustmentsSchema>;
export type BulkDeleteAssetAdjustmentsSchema = z.infer<typeof bulkDeleteAssetAdjustmentsSchema>;
export type AssetAdjustmentFormSchema = z.infer<typeof assetAdjustmentFormSchema>;
