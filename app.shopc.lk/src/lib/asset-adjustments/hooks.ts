import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import {
  AssetAdjustmentPaginatedResponse,
  AssetAdjustmentIdResponse,
  BulkAssetAdjustmentIdsResponse,
  BulkDeleteAssetAdjustmentResponse,
  SimpleAssetAdjustmentResponse,
  AssetAdjustmentResponse,
  CreateAssetAdjustmentDto,
  UpdateAssetAdjustmentDto,
  BulkCreateAssetAdjustmentDto,
  BulkDeleteAssetAdjustmentDto,
  PaginatedAssetAdjustmentsResponseDto,
} from "@/types/asset-adjustment";
import { ApiResponse } from "@/types/common";
import { GetAssetAdjustmentsSchema } from "./validations";
import {
  getAssetAdjustmentsTableData,
  getAssetAdjustmentsSlim,
  getAssetAdjustment,
  createAssetAdjustment,
  bulkCreateAssetAdjustments,
  updateAssetAdjustment,
  deleteAssetAdjustment,
  bulkDeleteAssetAdjustments,
} from "./queries";

// Hook to get asset adjustments table data with pagination and filters
export function useAssetAdjustmentsTableData(
  params: GetAssetAdjustmentsSchema,
  isDemo: boolean,
  enabled: boolean = true
): UseQueryResult<PaginatedAssetAdjustmentsResponseDto, Error> {
  return useQuery({
    queryKey: ["asset-adjustments", "table", params, isDemo],
    queryFn: () => getAssetAdjustmentsTableData(params, isDemo),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to get asset adjustments in slim format (for dropdowns/selects)
export function useAssetAdjustmentsSlim(
  isDemo: boolean,
  enabled: boolean = true
): UseQueryResult<SimpleAssetAdjustmentResponse, Error> {
  return useQuery({
    queryKey: ["asset-adjustments", "slim", isDemo],
    queryFn: () => getAssetAdjustmentsSlim(isDemo),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  });
}

// Hook to get a single asset adjustment by ID
export function useAssetAdjustment(
  id: string,
  isDemo: boolean,
  enabled: boolean = true
): UseQueryResult<AssetAdjustmentResponse, Error> {
  return useQuery({
    queryKey: ["asset-adjustments", id, isDemo],
    queryFn: () => getAssetAdjustment(id, isDemo),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to create a new asset adjustment
export function useCreateAssetAdjustment(isDemo: boolean) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAssetAdjustmentDto) =>
      createAssetAdjustment(data, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset adjustments queries
      queryClient.invalidateQueries({
        queryKey: ["asset-adjustments"],
      });
      // Also invalidate assets queries as asset adjustments affect asset values
      queryClient.invalidateQueries({
        queryKey: ["assets"],
      });
    },
  });
}

// Hook to bulk create asset adjustments
export function useBulkCreateAssetAdjustments(isDemo: boolean) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (assetAdjustments: BulkCreateAssetAdjustmentDto[]) =>
      bulkCreateAssetAdjustments(assetAdjustments, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset adjustments queries
      queryClient.invalidateQueries({
        queryKey: ["asset-adjustments"],
      });
      // Also invalidate assets queries as asset adjustments affect asset values
      queryClient.invalidateQueries({
        queryKey: ["assets"],
      });
    },
  });
}

// Hook to update an asset adjustment
export function useUpdateAssetAdjustment(isDemo: boolean) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAssetAdjustmentDto }) =>
      updateAssetAdjustment(id, data, isDemo),
    onSuccess: (_, { id }) => {
      // Invalidate and refetch asset adjustments queries
      queryClient.invalidateQueries({
        queryKey: ["asset-adjustments"],
      });
      // Invalidate the specific asset adjustment
      queryClient.invalidateQueries({
        queryKey: ["asset-adjustments", id, isDemo],
      });
      // Also invalidate assets queries as asset adjustments affect asset values
      queryClient.invalidateQueries({
        queryKey: ["assets"],
      });
    },
  });
}

// Hook to delete an asset adjustment
export function useDeleteAssetAdjustment(isDemo: boolean) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteAssetAdjustment(id, isDemo),
    onSuccess: (_, id) => {
      // Invalidate and refetch asset adjustments queries
      queryClient.invalidateQueries({
        queryKey: ["asset-adjustments"],
      });
      // Remove the specific asset adjustment from cache
      queryClient.removeQueries({
        queryKey: ["asset-adjustments", id, isDemo],
      });
      // Also invalidate assets queries as asset adjustments affect asset values
      queryClient.invalidateQueries({
        queryKey: ["assets"],
      });
    },
  });
}

// Hook to bulk delete asset adjustments
export function useBulkDeleteAssetAdjustments(isDemo: boolean) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkDeleteAssetAdjustmentDto) =>
      bulkDeleteAssetAdjustments(data, isDemo),
    onSuccess: (_, { ids }) => {
      // Invalidate and refetch asset adjustments queries
      queryClient.invalidateQueries({
        queryKey: ["asset-adjustments"],
      });
      // Remove the specific asset adjustments from cache
      ids.forEach((id) => {
        queryClient.removeQueries({
          queryKey: ["asset-adjustments", id, isDemo],
        });
      });
      // Also invalidate assets queries as asset adjustments affect asset values
      queryClient.invalidateQueries({
        queryKey: ["assets"],
      });
    },
  });
}
