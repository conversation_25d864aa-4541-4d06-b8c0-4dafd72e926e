import { ApiResponse } from "./common";

export enum AssetAdjustmentType {
  DEPRECIATION = "depreciation",
  REVALUATION = "revaluation",
  IMPAIRMENT = "impairment",
  CORRECTION = "correction",
}

// Backend DTO: AssetAdjustmentDto
export interface AssetAdjustmentDto {
  id: string;
  businessId: string;
  assetId: string;
  asset: {
    id: string;
    name: string;
    assetCode?: string;
  };
  adjustmentType: AssetAdjustmentType;
  adjustmentDate: string;
  effectivePeriod?: string;
  amount: string;
  bookValueBefore: string;
  bookValueAfter: string;
  description?: string;
  referenceNo?: string;
  status: string;
  journalEntryId?: string;
  journalEntry?: {
    id: string;
    entryNumber: string;
    description?: string;
  };
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreateAssetAdjustmentDto
export interface CreateAssetAdjustmentDto {
  assetId: string;
  adjustmentType: AssetAdjustmentType;
  adjustmentDate: string;
  effectivePeriod?: string;
  amount: string;
  bookValueBefore: string;
  bookValueAfter: string;
  description?: string;
  referenceNo?: string;
  status?: string;
  journalEntryId?: string;
}

// Backend DTO: UpdateAssetAdjustmentDto
export interface UpdateAssetAdjustmentDto {
  assetId?: string;
  adjustmentType?: AssetAdjustmentType;
  adjustmentDate?: string;
  effectivePeriod?: string;
  amount?: string;
  bookValueBefore?: string;
  bookValueAfter?: string;
  description?: string;
  referenceNo?: string;
  status?: string;
  journalEntryId?: string;
}

// Backend DTO: AssetAdjustmentSlimDto
export interface AssetAdjustmentSlimDto {
  id: string;
  asset: {
    id: string;
    name: string;
    assetCode?: string;
  };
  adjustmentType: AssetAdjustmentType;
  adjustmentDate: string;
  amount: string;
}

// Backend DTO: AssetAdjustmentListDto (for table display)
export interface AssetAdjustmentListDto {
  id: string;
  asset: {
    id: string;
    name: string;
    assetCode?: string;
  };
  adjustmentType: AssetAdjustmentType;
  adjustmentDate: string;
  effectivePeriod?: string;
  amount: string;
  bookValueBefore: string;
  bookValueAfter: string;
  status: string;
  referenceNo?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: PaginationMetaDto
export interface PaginationMetaDto {
  total: number;
  page: number;
  totalPages: number;
}

export interface PaginatedAssetAdjustmentsResponseDto {
  data: AssetAdjustmentListDto[];
  meta: PaginationMetaDto;
}

// Backend DTO: DeleteAssetAdjustmentResponseDto
export interface DeleteAssetAdjustmentResponseDto {
  message: string;
  id: string;
}

// Backend DTO: AssetAdjustmentIdResponseDto
export interface AssetAdjustmentIdResponseDto {
  id: string;
}

// Backend DTO: BulkAssetAdjustmentIdsResponseDto
export interface BulkAssetAdjustmentIdsResponseDto {
  ids: string[];
}

// Backend DTO: BulkDeleteAssetAdjustmentDto
export interface BulkDeleteAssetAdjustmentDto {
  ids: string[];
}

// Backend DTO: BulkDeleteAssetAdjustmentResponseDto
export interface BulkDeleteAssetAdjustmentResponseDto {
  deletedCount: number;
  deletedIds: string[];
}

// API Response wrappers
export interface AssetAdjustmentResponse extends ApiResponse<AssetAdjustmentDto> {}

export interface AssetAdjustmentListResponse
  extends ApiResponse<AssetAdjustmentListDto[]> {}

export interface AssetAdjustmentPaginatedResponse
  extends ApiResponse<PaginatedAssetAdjustmentsResponseDto | null> {}

export interface SimpleAssetAdjustmentResponse
  extends ApiResponse<AssetAdjustmentSlimDto[]> {}

export interface AssetAdjustmentIdResponse
  extends ApiResponse<AssetAdjustmentIdResponseDto> {}

export interface BulkAssetAdjustmentIdsResponse
  extends ApiResponse<BulkAssetAdjustmentIdsResponseDto> {}

export interface BulkDeleteAssetAdjustmentResponse
  extends ApiResponse<BulkDeleteAssetAdjustmentResponseDto> {}

// Table data interface - optimized for table display
export interface AssetAdjustmentTableData {
  id: string;
  assetId: string;
  assetName: string;
  assetCode?: string;
  adjustmentType: AssetAdjustmentType;
  adjustmentDate: string;
  effectivePeriod?: string;
  amount: string;
  bookValueBefore: string;
  bookValueAfter: string;
  status: string;
  referenceNo?: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Form values interface for UI components
export interface AssetAdjustmentFormValues {
  assetId: string;
  adjustmentType: AssetAdjustmentType;
  adjustmentDate: string;
  effectivePeriod?: string;
  amount: string;
  bookValueBefore: string;
  bookValueAfter: string;
  description?: string;
  referenceNo?: string;
  status?: string;
  journalEntryId?: string;
}

// Bulk create asset adjustment interface
export interface BulkCreateAssetAdjustmentDto extends CreateAssetAdjustmentDto {}

// Legacy aliases for backward compatibility (to be removed gradually)
export type AssetAdjustment = AssetAdjustmentDto;
export type SimpleAssetAdjustmentData = AssetAdjustmentSlimDto;
export type AssetAdjustmentPaginatedData = PaginatedAssetAdjustmentsResponseDto;

// Extended interfaces for frontend use
export interface AssetAdjustmentTableDataExtended extends AssetAdjustmentTableData {
  _id: string; // Legacy field for backward compatibility
  publicId: string; // Legacy field for backward compatibility
}
